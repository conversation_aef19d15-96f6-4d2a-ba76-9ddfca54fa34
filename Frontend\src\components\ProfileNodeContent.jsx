const ProfileNodeContent = ({ profile, onRemove }) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-2 border-2 border-green-300 flex justify-between items-center w-[160px] h-[40px] hover:shadow-lg transition-shadow">
      <div className="flex items-center space-x-2 flex-1 min-w-0">
        {profile.profile_photo ? (
          <img
            src={`http://localhost:5000/uploads/profiles/${profile.profile_photo}`}
            alt={profile.full_name}
            className="w-8 h-8 rounded-full object-cover border-2 border-green-200 flex-shrink-0"
          />
        ) : (
          <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center border-2 border-green-200 flex-shrink-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-green-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
        )}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-xs text-gray-800 truncate leading-tight">{profile.full_name}</h3>
          <p className="text-xs text-gray-600 truncate leading-tight">{profile.designation}</p>
        </div>
      </div>
      <button
        onClick={onRemove}
        className="text-red-500 hover:text-red-700 ml-1 bg-red-50 hover:bg-red-100 rounded-full p-1 transition-colors flex-shrink-0"
        title="Remove from diagram"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
          <path
            fillRule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>
    </div>
  )
}

export default ProfileNodeContent
