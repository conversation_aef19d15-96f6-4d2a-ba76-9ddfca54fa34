"use client"

import { useState, useEffect } from "react"
import { useParams, useNavigate } from "react-router-dom"
import axios from "axios"
import DraggableProfileCard from "../components/DraggableProfileCard"
import ProfileNodeContent from "../components/ProfileNodeContent"
import DragDropInstructions from "../components/DragDropInstructions"
import { toPng } from "html-to-image"

const FishboneDiagram = () => {
  const { id, opportunityId, role } = useParams()
  const navigate = useNavigate()
  const [account, setAccount] = useState(null)
  const [opportunity, setOpportunity] = useState(null)
  const [profiles, setProfiles] = useState([])
  const [loading, setLoading] = useState(true)
  const [toast, setToast] = useState({ message: "", type: "", visible: false })
  const [departments, setDepartments] = useState([])
  const [nodePlacements, setNodePlacements] = useState([])
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState(null)
  const [activeId, setActiveId] = useState(null)
  const [newDepartment, setNewDepartment] = useState("")
  const [showInstructions, setShowInstructions] = useState(true)

  const showToast = (message, type) => {
    setToast({ message, type, visible: true })
    setTimeout(() => setToast({ message: "", type: "", visible: false }), 3000)
  }

  const fetchAccount = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/accounts/${id}`)
      setAccount(res.data)
    } catch (error) {
      console.error("Error fetching account:", error)
      setError("Failed to load account details")
      showToast("Error fetching account details", "error")
    }
  }

  const fetchOpportunity = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/opportunities/account/${id}`)
      const opp = res.data.find((o) => o.id === Number.parseInt(opportunityId))
      if (opp) {
        setOpportunity(opp)
      } else {
        setError("Opportunity not found")
        showToast("Opportunity not found", "error")
      }
    } catch (error) {
      console.error("Error fetching opportunity:", error)
      setError("Failed to load opportunity details")
      showToast("Error fetching opportunity details", "error")
    }
  }

  const fetchProfiles = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/profiles/account/${id}`)
      setProfiles(res.data)
    } catch (error) {
      console.error("Error fetching profiles:", error)
      setError("Failed to load profiles")
      showToast("Error fetching profiles", "error")
    }
  }

  const fetchFishboneData = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/fishbone/opportunity/${opportunityId}`)
      console.log("Fetched fishbone data:", res.data)

      if (res.data) {
        const fetchedDepartments = res.data.departments || []
        const fetchedPlacements = res.data.node_placements || []

        setDepartments(fetchedDepartments)
        setNodePlacements(fetchedPlacements)

        console.log("Set departments:", fetchedDepartments)
        console.log("Set node placements:", fetchedPlacements)
      }
    } catch (error) {
      console.error("Error fetching fishbone data:", error)
      setError("Failed to load diagram data")
      showToast("Error fetching diagram data", "error")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([fetchAccount(), fetchOpportunity(), fetchProfiles(), fetchFishboneData()])
      } catch (error) {
        console.error("Error loading data:", error)
        setError("Failed to load data")
        setLoading(false)
      }
    }

    loadData()
  }, [id, opportunityId])

  const saveFishboneData = async (updatedDepartments, updatedPlacements) => {
    setIsSaving(true)
    try {
      console.log("Saving mind map data:", {
        opportunity_id: Number.parseInt(opportunityId),
        departments: updatedDepartments,
        node_placements: updatedPlacements,
      })

      const response = await axios.post(`http://localhost:5000/api/fishbone/save`, {
        opportunity_id: Number.parseInt(opportunityId),
        departments: updatedDepartments,
        node_placements: updatedPlacements,
      })

      console.log("Save response:", response.data)
      showToast("Mind map saved successfully", "success")
    } catch (error) {
      console.error("Error saving mind map data:", error.response?.data || error.message)
      showToast(`Error saving mind map: ${error.response?.data?.error || error.message}`, "error")
    } finally {
      setIsSaving(false)
    }
  }

  const handleAddDepartment = () => {
    if (newDepartment.trim() !== "") {
      const updatedDepartments = [...(departments || []), newDepartment.trim()]
      console.log("Adding department:", newDepartment.trim(), "New departments:", updatedDepartments)
      setDepartments(updatedDepartments)
      setNewDepartment("")
      saveFishboneData(updatedDepartments, nodePlacements || [])
    }
  }

  const handleRemoveDepartment = (index) => {
    console.log("Removing department at index:", index)
    const updatedDepartments = [...(departments || [])]
    updatedDepartments.splice(index, 1)

    const updatedPlacements = (nodePlacements || []).filter((p) => p.department_index !== index)

    updatedPlacements.forEach((p) => {
      if (p.department_index > index) {
        p.department_index -= 1
      }
    })

    setDepartments(updatedDepartments)
    setNodePlacements(updatedPlacements)
    saveFishboneData(updatedDepartments, updatedPlacements)
  }

  const handleRemoveProfileFromDepartment = (profileId, departmentIndex) => {
    console.log("Removing profile from specific department:", profileId, "department:", departmentIndex)
    const updatedPlacements = (nodePlacements || []).filter(
      (p) => !(p.profile_id === profileId && p.department_index === departmentIndex),
    )
    setNodePlacements(updatedPlacements)
    saveFishboneData(departments || [], updatedPlacements)
  }

  const getProfilesForDepartment = (departmentIndex) => {
    if (!nodePlacements || !profiles) return []

    const placementsForDepartment = nodePlacements.filter((p) => p.department_index === departmentIndex)
    return placementsForDepartment
      .map((p) => {
        return profiles.find((profile) => profile.id === p.profile_id)
      })
      .filter(Boolean)
  }

  const handleExportDiagram = () => {
    const element = document.getElementById("mind-map-diagram")
    if (!element) return

    toPng(element)
      .then((dataUrl) => {
        const link = document.createElement("a")
        link.download = `${opportunity?.name || "mind-map"}-diagram.png`
        link.href = dataUrl
        link.click()
      })
      .catch((error) => {
        console.error("Error exporting diagram:", error)
        showToast("Error exporting diagram", "error")
      })
  }

  const goBack = () => {
    navigate(`/admin/${role}/account/${id}`)
  }

  const handleAddProfileClick = () => {
    navigate(`/admin/${role}/account/${id}/add-profile`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 flex items-center justify-center relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-200/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-32 right-16 w-96 h-96 bg-indigo-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="bg-white rounded-2xl shadow-xl shadow-blue-200/50 border border-white/20 p-8 relative z-10">
          <div className="flex flex-col items-center space-y-3">
            <div className="w-12 h-12 border-4 border-blue-500/30 border-t-blue-600 rounded-full animate-spin"></div>
            <div className="text-xl font-bold bg-gradient-to-r from-gray-800 to-blue-800 bg-clip-text text-transparent">
              Loading Mind Map...
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 flex items-center justify-center relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-72 h-72 bg-red-200/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-32 right-16 w-96 h-96 bg-rose-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="bg-white rounded-2xl shadow-xl shadow-red-200/50 border border-white/20 p-8 max-w-md w-full mx-4 relative z-10">
          <div className="flex flex-col items-center space-y-4">
            <svg className="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <div className="text-xl font-bold bg-gradient-to-r from-gray-800 to-red-800 bg-clip-text text-transparent text-center">
              Error
            </div>
            <p className="text-gray-700 text-center leading-relaxed text-sm">{error}</p>
            <button
              onClick={goBack}
              className="group bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center font-semibold text-sm"
            >
              <svg
                className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span>Go Back</span>
            </button>
          </div>
        </div>
      </div>
    )
  }

  const departmentsArray = Array.isArray(departments) ? departments : []

  const departmentColors = [
    { bg: "bg-yellow-400", border: "border-yellow-500", line: "#FFD700" },
    { bg: "bg-teal-400", border: "border-teal-500", line: "#2DD4BF" },
    { bg: "bg-orange-500", border: "border-orange-600", line: "#F97316" },
    { bg: "bg-purple-500", border: "border-purple-600", line: "#A855F7" },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 p-3 md:p-6 relative overflow-hidden">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-32 right-16 w-96 h-96 bg-indigo-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-purple-200/20 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-6">
          <button
            onClick={goBack}
            className="group flex items-center text-gray-700 hover:text-blue-600 transition-all duration-300 bg-white hover:bg-white/90 backdrop-blur-sm rounded-lg px-4 py-3 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg hover:shadow-blue-200/30"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            <span className="font-semibold text-sm">Back to Account</span>
          </button>

          <div className="flex flex-col sm:flex-row gap-2">
            <button
              onClick={handleExportDiagram}
              className="group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-4 py-3 rounded-lg shadow-lg hover:shadow-xl hover:shadow-blue-200/50 transition-all duration-300 flex items-center text-sm"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
              Export Diagram
            </button>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-xl shadow-blue-200/50 border border-white/20 p-6 md:p-8 mb-6 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-xl"></div>

          <div className="relative z-10">
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-indigo-800 bg-clip-text text-transparent mb-3">
              {account?.name} / {opportunity?.name}
            </h1>
            <p className="text-gray-600 text-base leading-relaxed">{opportunity?.description}</p>
          </div>
        </div>

        {showInstructions && <DragDropInstructions />}

        <div className="bg-white rounded-2xl shadow-xl shadow-green-200/50 border border-white/20 p-6 md:p-8 mb-6 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-green-400/10 to-emerald-400/10 rounded-full blur-2xl"></div>

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-4 relative z-10">
            <div>
              <h2 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-gray-800 to-green-800 bg-clip-text text-transparent mb-2">
                Available Profiles
              </h2>
              <p className="text-gray-600 text-sm leading-relaxed">
                Drag and drop profiles onto the mind map departments. Profiles can be added to multiple departments.
                <strong> Double-click on a profile to view details.</strong>
              </p>
            </div>

            <button
              onClick={handleAddProfileClick}
              className="group bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 text-white font-semibold px-4 py-3 rounded-lg shadow-lg hover:shadow-xl hover:shadow-green-200/50 transition-all duration-300 flex items-center whitespace-nowrap text-sm"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                  clipRule="evenodd"
                />
              </svg>
              Add Profile
            </button>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-7 gap-3 relative z-10">
            {profiles.map((profile) => (
              <DraggableProfileCard
                key={`profile-card-${profile.id}`}
                profile={profile}
                id={`profile-card-${profile.id}`}
              />
            ))}
          </div>
        </div>

        <div
          id="mind-map-diagram"
          className="bg-white rounded-2xl shadow-xl shadow-purple-200/50 border border-white/20 p-4 md:p-6 overflow-x-auto relative"
        >
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full blur-xl"></div>

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-4 relative z-10">
            <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-gray-800 to-purple-800 bg-clip-text text-transparent">
              Mind Map
            </h2>
            {isSaving && (
              <div className="flex items-center space-x-2 bg-blue-50/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-blue-200/50">
                <div className="w-3 h-3 border-2 border-blue-500/30 border-t-blue-600 rounded-full animate-spin"></div>
                <span className="text-blue-600 font-medium text-sm">Saving...</span>
              </div>
            )}
          </div>

          <div className="mb-4 relative z-10">
            <div className="flex flex-col sm:flex-row gap-2">
              <input
                type="text"
                value={newDepartment}
                onChange={(e) => setNewDepartment(e.target.value)}
                className="flex-1 border-0 bg-white/80 backdrop-blur-sm rounded-lg p-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400 text-sm"
                placeholder="Enter department name..."
              />
              <button
                onClick={handleAddDepartment}
                className="group bg-gradient-to-r from-purple-600 to-purple-700 text-white px-6 py-3 rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-purple-200/50 font-semibold whitespace-nowrap text-sm"
              >
                <span className="group-hover:scale-105 transition-transform duration-300 inline-block">
                  Add Department
                </span>
              </button>
            </div>
          </div>

          <div className="relative w-full overflow-x-auto pb-6 z-10">
            <div className="flex justify-center items-center">
              <div className="relative" style={{ width: "1200px", height: "600px" }}>
                {/* Central Spine */}
                <div
                  className="absolute h-1 bg-gradient-to-r from-gray-400 to-gray-600 shadow-sm z-5"
                  style={{
                    top: "300px",
                    left: "100px",
                    right: "100px",
                  }}
                ></div>

                {/* Central Node - at the right end of spine */}
                <div
                  className="absolute z-20"
                  style={{
                    top: "300px",
                    right: "50px",
                    transform: "translateY(-50%)",
                  }}
                >
                  <div className="bg-gradient-to-r from-slate-800 to-gray-800 text-white rounded-lg px-8 py-4 text-xl font-bold shadow-xl border-4 border-white/20 backdrop-blur-sm">
                    <span className="bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                      Mind Map
                    </span>
                  </div>
                </div>

                {departmentsArray.map((department, index) => {
                  const totalDepartments = departmentsArray.length
                  const isAbove = index % 2 === 0 // Alternate above/below
                  const branchLength = 120 // Length of the angled branch

                  // Calculate position along the spine
                  const spineStart = 100 // Start position from left
                  const spineEnd = 950 // End position (before central node)
                  const spineLength = spineEnd - spineStart
                  const departmentX = spineStart + (spineLength / (totalDepartments + 1)) * (index + 1)

                  // Calculate branch end position at exactly 45-degree angle
                  const angle = isAbove ? -45 : 45 // degrees
                  const angleRad = (angle * Math.PI) / 180
                  const branchEndX = departmentX + branchLength * Math.cos(angleRad)
                  const branchEndY = 300 + branchLength * Math.sin(angleRad) // 300 is center Y (spine level)

                  const colorIndex = index % departmentColors.length
                  const color = departmentColors[colorIndex]

                  const departmentProfiles = getProfilesForDepartment(index)

                  return (
                    <div key={index}>
                      {/* Angled Branch Line */}
                      <svg
                        className="absolute top-0 left-0 w-full h-full pointer-events-none z-10"
                        style={{ overflow: "visible" }}
                      >
                        <line
                          x1={departmentX}
                          y1={300}
                          x2={branchEndX}
                          y2={branchEndY}
                          stroke={color.line}
                          strokeWidth="4"
                          className="drop-shadow-sm"
                        />
                      </svg>

                      {/* Department Node */}
                      <div className="absolute" style={{ left: `${branchEndX}px`, top: `${branchEndY}px`, zIndex: 20 }}>
                        <div
                          id={`department-${index}`}
                          data-droppable="true"
                          data-department-index={index}
                          className={`${color.bg} rounded-lg px-4 py-2 font-semibold shadow-lg relative border-2 border-white/30 backdrop-blur-sm hover:scale-105 transition-all duration-300 text-sm`}
                          style={{
                            cursor: activeId ? "copy" : "default",
                            minWidth: "120px",
                            minHeight: "40px",
                            transform: "translate(-50%, -50%)",
                            boxShadow: `0 15px 20px -5px rgba(0, 0, 0, 0.1), 0 8px 8px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px ${color.line}33`,
                          }}
                          onDragOver={(e) => {
                            e.preventDefault()
                            e.currentTarget.classList.add("opacity-70")
                          }}
                          onDragLeave={(e) => {
                            e.preventDefault()
                            e.currentTarget.classList.remove("opacity-70")
                          }}
                          onDrop={(e) => {
                            e.preventDefault()
                            e.currentTarget.classList.remove("opacity-70")

                            try {
                              const data = JSON.parse(e.dataTransfer.getData("text/plain"))
                              if (data && data.type === "profile" && data.profileId) {
                                console.log(`Dropping profile ${data.profileId} onto department index ${index}`)

                                const alreadyExists = (nodePlacements || []).some(
                                  (p) => p.profile_id === data.profileId && p.department_index === index,
                                )

                                if (alreadyExists) {
                                  showToast("Profile is already in this department", "error")
                                  return
                                }

                                const updatedPlacements = [...(nodePlacements || [])]
                                updatedPlacements.push({
                                  profile_id: data.profileId,
                                  department_index: index,
                                })

                                setNodePlacements(updatedPlacements)
                                saveFishboneData(departments || [], updatedPlacements)
                                showToast("Profile added to department", "success")
                              }
                            } catch (error) {
                              console.error("Error processing drop:", error)
                            }
                          }}
                        >
                          <div className="text-center text-white text-base font-bold drop-shadow-lg">{department}</div>
                          <button
                            onClick={() => handleRemoveDepartment(index)}
                            className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl border-2 border-white/50"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-3 w-3"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>

                      {/* Profile Sub-branches */}
                      {departmentProfiles.length > 0 &&
                        departmentProfiles.map((profile, profileIndex) => {
                          // Improved profile positioning logic
                          const profileSpacing = 80 // Distance between profiles
                          const baseDistanceFromDepartment = 140 // Base distance from department node

                          // Calculate the total width needed for all profiles
                          const totalProfiles = departmentProfiles.length
                          const totalWidth = (totalProfiles - 1) * profileSpacing

                          // Calculate starting position to center the profiles
                          const startOffset = -totalWidth / 2
                          const profileOffset = startOffset + profileIndex * profileSpacing

                          // Calculate profile position perpendicular to the department branch
                          const perpAngle = isAbove ? -90 : 90 // Perpendicular to department branch
                          const perpAngleRad = (perpAngle * Math.PI) / 180

                          // Position profiles extending perpendicular from department node
                          const profileCenterX =
                            branchEndX +
                            baseDistanceFromDepartment * Math.cos(perpAngleRad) +
                            profileOffset * Math.cos(angleRad)
                          const profileCenterY =
                            branchEndY +
                            baseDistanceFromDepartment * Math.sin(perpAngleRad) +
                            profileOffset * Math.sin(angleRad)

                          // Profile node dimensions
                          const profileNodeWidth = 160
                          const profileNodeHeight = 40

                          // Calculate final position for the profile node (top-left corner)
                          const profileNodeX = profileCenterX - profileNodeWidth / 2
                          const profileNodeY = profileCenterY - profileNodeHeight / 2

                          return (
                            <div key={`${profile.id}-${index}`}>
                              {/* Profile Branch Line - from department node to profile */}
                              <svg
                                className="absolute top-0 left-0 w-full h-full pointer-events-none z-10"
                                style={{ overflow: "visible" }}
                              >
                                <line
                                  x1={branchEndX}
                                  y1={branchEndY}
                                  x2={profileCenterX}
                                  y2={profileCenterY}
                                  stroke={color.line}
                                  strokeWidth="2"
                                  opacity="0.7"
                                  strokeDasharray="4,4"
                                />
                              </svg>

                              {/* Profile Node */}
                              <div
                                className="absolute z-20"
                                style={{
                                  left: `${profileNodeX}px`,
                                  top: `${profileNodeY}px`,
                                }}
                              >
                                <ProfileNodeContent
                                  profile={profile}
                                  onRemove={() => handleRemoveProfileFromDepartment(profile.id, index)}
                                />
                              </div>
                            </div>
                          )
                        })}
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {toast.visible && (
        <div className="fixed top-6 right-6 z-50 animate-slide-in-right">
          <div
            className={`px-6 py-4 rounded-2xl shadow-2xl backdrop-blur-xl border text-white transform transition-all duration-500 ${
              toast.type === "success"
                ? "bg-gradient-to-r from-green-500/90 to-emerald-600/90 border-green-300/30 shadow-green-200/50"
                : "bg-gradient-to-r from-red-500/90 to-rose-600/90 border-red-300/30 shadow-red-200/50"
            }`}
          >
            <div className="flex items-center space-x-3">
              {toast.type === "success" ? (
                <svg className="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
              <span className="font-medium text-sm">{toast.message}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FishboneDiagram
